<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付中心 - Miru</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --dark: #2d3436;
            --light: #f5f6fa;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --border-radius: 16px;
            --border-radius-sm: 10px;
            --border-radius-lg: 24px;
            --transition: all 0.3s ease;
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --card-shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            -webkit-tap-highlight-color: transparent;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            touch-action: manipulation;
        }

        body {
            background: linear-gradient(135deg, #f9fbfd 0%, #e6ebf5 100%);
            color: var(--dark);
            position: relative;
        }

        /* 返回按钮 */
        .back-btn {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(108, 92, 231, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: 1.2rem;
            z-index: 10;
            transition: var(--transition);
        }

        .back-btn:hover {
            background: rgba(108, 92, 231, 0.2);
            transform: translateX(-3px);
        }

        .container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 60px 20px 20px;
            overflow-y: auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }

        .header p {
            color: #666;
            font-size: 1rem;
        }

        /* 商品卡片 */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .product-card {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: 20px 15px;
            box-shadow: var(--card-shadow);
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--card-shadow-hover);
        }

        .product-card.selected {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
        }

        .product-icon {
            width: 50px;
            height: 50px;
            margin: 0 auto 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            transition: var(--transition);
        }

        .vip .product-icon {
            background: linear-gradient(135deg, var(--warning), #e17055);
        }

        .points .product-icon {
            background: linear-gradient(135deg, var(--success), #00b894);
        }

        .product-card:hover .product-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .product-name {
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 1rem;
        }

        .product-price {
            font-weight: 700;
            color: var(--primary);
            font-size: 1.2rem;
        }

        .product-desc {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }

        /* 支付方式 */
        .payment-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: var(--primary);
        }

        .payment-methods {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .payment-method {
            flex: 1;
            padding: 15px;
            border-radius: var(--border-radius);
            background: white;
            box-shadow: var(--card-shadow);
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: var(--transition);
            border: 2px solid transparent;
        }

        .payment-method.selected {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
        }

        .payment-method i {
            font-size: 2rem;
            margin-bottom: 10px;
            transition: var(--transition);
        }

        .payment-method:hover i {
            transform: scale(1.1);
        }

        .payment-method.alipay i {
            color: #009fe8;
        }

        .payment-method.wechat i {
            color: #2aae67;
        }

        .payment-method span {
            font-weight: 500;
        }

        /* 购买按钮 */
        .purchase-btn {
            display: block;
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 10px 25px rgba(108, 92, 231, 0.3);
            position: relative;
            overflow: hidden;
        }

        .purchase-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.4);
        }

        .purchase-btn:active {
            transform: translateY(1px);
        }

        .purchase-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 支付链接区域 */
        .payment-link-section {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: 20px;
            box-shadow: var(--card-shadow);
            margin-top: 30px;
            display: none;
        }

        .payment-link-section.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .payment-link-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .payment-link-header i {
            font-size: 1.5rem;
            color: var(--primary);
            margin-right: 10px;
        }

        .payment-link-content {
            position: relative;
        }

        .payment-link {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius-sm);
            font-size: 0.9rem;
            background: #f9f9f9;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .copy-btn {
            position: absolute;
            right: 5px;
            top: 5px;
            padding: 7px 12px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            transition: var(--transition);
        }

        .copy-btn:hover {
            background: var(--primary-light);
        }

        .payment-instruction {
            margin-top: 15px;
            padding: 15px;
            background: rgba(253, 203, 110, 0.1);
            border-radius: var(--border-radius);
            border: 1px dashed rgba(253, 203, 110, 0.5);
            font-size: 0.9rem;
        }

        .payment-instruction p {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .payment-instruction i {
            margin-right: 10px;
            color: #e17055;
        }

        /* 支付状态 */
        .payment-status {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--card-shadow);
            text-align: center;
            display: none;
        }

        .payment-status.active {
            display: block;
            animation: slideUp 0.5s ease;
        }

        /* 错误提示 */
        .error-message {
            background: rgba(214, 48, 49, 0.1);
            border: 1px solid rgba(214, 48, 49, 0.3);
            color: var(--danger);
            padding: 15px;
            border-radius: var(--border-radius);
            margin: 15px 0;
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .error-message.active {
            display: block;
        }

        .error-message i {
            margin-right: 10px;
        }

        /* 重试按钮 */
        .retry-btn {
            background: var(--danger);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            margin-top: 10px;
            transition: var(--transition);
        }

        .retry-btn:hover {
            background: #b71c1c;
            transform: translateY(-2px);
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 5px solid rgba(108, 92, 231, 0.2);
            border-top-color: var(--primary);
            border-radius: 50%;
            margin: 0 auto 20px;
            animation: spin 1s linear infinite;
        }

        .status-text {
            font-size: 1.1rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .progress-bar {
            height: 8px;
            background: #eee;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 10px;
            width: 0%;
            transition: width 0.5s ease;
        }

        .timer {
            font-size: 0.9rem;
            color: #666;
        }

        .success-message {
            color: var(--success);
            font-weight: 600;
            font-size: 1.2rem;
            margin-top: 15px;
            display: none;
        }

        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* 响应式调整 */
        @media (min-width: 768px) {
            .container {
                padding: 70px 30px 30px;
            }
            
            .products-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .payment-methods {
                flex-direction: row;
            }
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="index.html" class="back-btn">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <div class="header">
            <h1>支付中心</h1>
            <p>选择您需要的服务，快速完成支付</p>
        </div>

        <!-- 商品选择 -->
        <div class="payment-section">
            <h2 class="section-title"><i class="fas fa-gift"></i> 选择商品</h2>
            <div class="products-grid" id="productsGrid">
                <!-- 商品卡片由JS动态生成 -->
            </div>
        </div>

        <!-- 支付方式 -->
        <div class="payment-section">
            <h2 class="section-title"><i class="fas fa-wallet"></i> 支付方式</h2>
            <div class="payment-methods">
                <div class="payment-method wechat" data-type="wechat">
                    <i class="fab fa-weixin"></i>
                    <span>微信支付</span>
                </div>
                <div class="payment-method alipay" data-type="alipay">
                    <i class="fab fa-alipay"></i>
                    <span>支付宝支付</span>
                </div>
            </div>
        </div>

        <!-- 购买按钮 -->
        <button id="purchaseBtn" class="purchase-btn" disabled>
            立即购买
        </button>

        <!-- 错误提示 -->
        <div class="error-message" id="errorMessage">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorText">创建订单失败，请稍后重试</span>
            <br>
            <button class="retry-btn" id="retryBtn">重试</button>
        </div>

        <!-- 支付链接区域 -->
        <div class="payment-link-section" id="paymentLinkSection">
            <div class="payment-link-header">
                <i class="fas fa-link"></i>
                <h3>支付链接</h3>
            </div>
            <div class="payment-link-content">
                <input type="text" id="paymentLink" class="payment-link" readonly>
                <button id="copyBtn" class="copy-btn">
                    <i class="fas fa-copy"></i> 复制
                </button>
            </div>
            <div class="payment-instruction">
                <p><i class="fas fa-info-circle"></i> 请复制上方链接，根据您选择的支付方式打开：</p>
                <p><i class="fab fa-weixin"></i> 微信支付：复制链接后在微信中打开</p>
                <p><i class="fab fa-alipay"></i> 支付宝支付：复制链接后在浏览器中打开</p>
            </div>
        </div>

        <!-- 支付状态 -->
        <div class="payment-status" id="paymentStatus">
            <div class="spinner"></div>
            <div class="status-text">正在等待支付完成...</div>
            <div class="progress-bar">
                <div class="progress" id="progressBar"></div>
            </div>
            <div class="timer">剩余时间: <span id="timer">10:00</span></div>
            <div class="success-message" id="successMessage">
                <i class="fas fa-check-circle"></i> 支付成功！服务已激活
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 商品数据
            const products = [
                { id: 78, name: "7天会员", price: 6.99, type: "vip", days: 7, category: "vip", icon: "crown" },
                { id: 79, name: "30天会员", price: 9.99, type: "vip", days: 30, category: "vip", icon: "gem" },
                { id: 80, name: "半年会员", price: 48.99, type: "vip", days: 180, category: "vip", icon: "star" },
                { id: 81, name: "永久会员", price: 68.99, type: "vip", days: 9999, category: "vip", icon: "infinity" },
                { id: 82, name: "1积分", price: 0.8, type: "points", points: 1, category: "points", icon: "coins" },
                { id: 83, name: "5积分", price: 4.0, type: "points", points: 5, category: "points", icon: "coins" },
                { id: 84, name: "10积分", price: 7.0, type: "points", points: 10, category: "points", icon: "coins" }
            ];

            // DOM元素
            const productsGrid = document.getElementById('productsGrid');
            const purchaseBtn = document.getElementById('purchaseBtn');
            const paymentLinkSection = document.getElementById('paymentLinkSection');
            const paymentLink = document.getElementById('paymentLink');
            const copyBtn = document.getElementById('copyBtn');
            const paymentStatus = document.getElementById('paymentStatus');
            const progressBar = document.getElementById('progressBar');
            const timerDisplay = document.getElementById('timer');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            const retryBtn = document.getElementById('retryBtn');

            // 状态变量
            let selectedProduct = null;
            let selectedPayment = null;
            let currentOrderId = null;
            let checkCount = 0;
            let timerInterval = null;
            let paymentCheckInterval = null;
            let timeLeft = 600; // 10分钟（600秒）
            let retryCount = 0;
            const maxRetries = 3;

            // 渲染商品列表
            function renderProducts() {
                productsGrid.innerHTML = '';
                
                products.forEach(product => {
                    const card = document.createElement('div');
                    card.className = `product-card ${product.category}`;
                    card.dataset.id = product.id;
                    
                    card.innerHTML = `
                        <div class="product-icon">
                            <i class="fas fa-${product.icon}"></i>
                        </div>
                        <div class="product-name">${product.name}</div>
                        <div class="product-price">¥${product.price.toFixed(2)}</div>
                        <div class="product-desc">
                            ${product.type === 'vip' ? `${product.days}天会员` : `${product.points}积分`}
                        </div>
                    `;
                    
                    card.addEventListener('click', () => {
                        // 移除之前的选择
                        document.querySelectorAll('.product-card').forEach(c => {
                            c.classList.remove('selected');
                        });
                        
                        // 添加当前选择
                        card.classList.add('selected');
                        selectedProduct = product;
                        
                        // 如果已选择支付方式，启用购买按钮
                        if (selectedPayment) {
                            purchaseBtn.disabled = false;
                        }
                        
                        // 添加动画效果
                        card.style.animation = 'pulse 0.5s';
                        setTimeout(() => {
                            card.style.animation = '';
                        }, 500);
                    });
                    
                    productsGrid.appendChild(card);
                });
            }

            // 设置支付方式选择
            function setupPaymentMethods() {
                const paymentMethods = document.querySelectorAll('.payment-method');
                
                paymentMethods.forEach(method => {
                    method.addEventListener('click', () => {
                        // 移除之前的选择
                        paymentMethods.forEach(m => {
                            m.classList.remove('selected');
                        });
                        
                        // 添加当前选择
                        method.classList.add('selected');
                        selectedPayment = method.dataset.type;
                        
                        // 如果已选择商品，启用购买按钮
                        if (selectedProduct) {
                            purchaseBtn.disabled = false;
                        }
                    });
                });
            }

            // 生成link参数（当前时间YmdHi格式的MD5值）
            function generateLink() {
                const now = new Date();
                const timeString = now.getFullYear() + 
                                  padZero(now.getMonth() + 1) + 
                                  padZero(now.getDate()) + 
                                  padZero(now.getHours()) + 
                                  padZero(now.getMinutes());
                
                return CryptoJS.MD5(timeString).toString();
            }

            // 辅助函数：补零
            function padZero(num) {
                return num < 10 ? `0${num}` : num;
            }

            // 显示错误信息
            function showError(message, showRetry = true) {
                errorText.textContent = message;
                errorMessage.classList.add('active');
                retryBtn.style.display = showRetry ? 'inline-block' : 'none';

                // 滚动到错误信息
                errorMessage.scrollIntoView({ behavior: 'smooth' });

                // 3秒后自动隐藏（如果不需要重试按钮）
                if (!showRetry) {
                    setTimeout(() => {
                        hideError();
                    }, 3000);
                }
            }

            // 隐藏错误信息
            function hideError() {
                errorMessage.classList.remove('active');
            }

            // 创建订单（带重试机制）
            async function createOrder(isRetry = false) {
                if (!selectedProduct || !selectedPayment) return;

                // 隐藏之前的错误信息
                hideError();

                // 显示加载状态
                purchaseBtn.disabled = true;
                const loadingText = isRetry ? '重试中...' : '创建订单中...';
                purchaseBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${loadingText}`;

                try {
                    // 构建请求URL
                    const customerContact = '19636448523';
                    const url = `https://cloudshop.qnm6.top/create_order.php?product_id=${selectedProduct.id}&pay_type=${selectedPayment}&customer_contact=${customerContact}`;

                    // 发送请求（增加超时控制）
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

                    const response = await fetch(url, {
                        signal: controller.signal,
                        headers: {
                            'Cache-Control': 'no-cache'
                        }
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`网络错误: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.payment_url && data.order_id) {
                        // 重置重试计数
                        retryCount = 0;

                        // 保存订单ID
                        currentOrderId = data.order_id;

                        // 缩短URL
                        const shortUrl = await shortenUrl(data.payment_url);

                        // 显示支付链接
                        paymentLink.value = shortUrl;
                        paymentLinkSection.classList.add('active');

                        // 滚动到支付链接区域
                        paymentLinkSection.scrollIntoView({ behavior: 'smooth' });

                        // 启动支付状态检查
                        startPaymentCheck();

                        // 启动倒计时
                        startTimer();
                    } else {
                        throw new Error(data.message || '创建订单失败，服务器返回异常数据');
                    }
                } catch (error) {
                    console.error('创建订单失败:', error);

                    let errorMsg = '创建订单失败，请稍后重试';

                    if (error.name === 'AbortError') {
                        errorMsg = '请求超时，请检查网络连接后重试';
                    } else if (error.message.includes('网络错误')) {
                        errorMsg = '网络连接异常，请检查网络后重试';
                    } else if (error.message.includes('Failed to fetch')) {
                        errorMsg = '无法连接到服务器，请检查网络连接';
                    } else if (error.message) {
                        errorMsg = error.message;
                    }

                    // 显示错误信息
                    showError(errorMsg, retryCount < maxRetries);
                    retryCount++;
                } finally {
                    // 重置按钮状态
                    purchaseBtn.disabled = false;
                    purchaseBtn.innerHTML = '立即购买';
                }
            }

            // 缩短URL（增加错误处理）
            async function shortenUrl(longUrl) {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

                    const response = await fetch(`https://tinyurl.com/api-create.php?url=${encodeURIComponent(longUrl)}`, {
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    if (response.ok) {
                        const shortUrl = await response.text();
                        return shortUrl.trim();
                    } else {
                        throw new Error('URL缩短服务异常');
                    }
                } catch (error) {
                    console.error('URL缩短失败:', error);
                    return longUrl; // 如果缩短失败，返回原始URL
                }
            }

            // 开始支付状态检查（增强错误处理）
            function startPaymentCheck() {
                // 显示支付状态区域
                paymentStatus.classList.add('active');
                paymentStatus.scrollIntoView({ behavior: 'smooth' });

                // 每30秒检查一次支付状态
                paymentCheckInterval = setInterval(async () => {
                    if (checkCount >= 20) { // 10分钟（20次 * 30秒）
                        clearInterval(paymentCheckInterval);
                        showError('支付超时，请重新下单', false);
                        return;
                    }

                    try {
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

                        const response = await fetch(`https://cloudshop.qnm6.top/check_payment_status.php?order_id=${currentOrderId}`, {
                            signal: controller.signal,
                            headers: {
                                'Cache-Control': 'no-cache'
                            }
                        });

                        clearTimeout(timeoutId);

                        if (!response.ok) {
                            throw new Error(`状态检查失败: ${response.status}`);
                        }

                        const data = await response.json();

                        if (data.message && data.message.includes('支付成功')) {
                            // 支付成功
                            clearInterval(paymentCheckInterval);
                            clearInterval(timerInterval);

                            // 调用充值接口
                            await activateService();

                            // 显示成功消息
                            document.querySelector('.spinner').style.display = 'none';
                            successMessage.style.display = 'block';
                            progressBar.style.width = '100%';
                        }

                        checkCount++;
                    } catch (error) {
                        console.error('支付状态检查失败:', error);
                        // 不中断检查，继续下次尝试
                    }
                }, 30000); // 30秒
            }

            // 激活服务（充值积分/会员）- 增强错误处理
            async function activateService() {
                // 生成link参数
                const link = generateLink();
                const username = localStorage.getItem('miru_username');

                if (!username) {
                    console.error('用户名未找到');
                    showError('用户信息异常，请重新登录', false);
                    return;
                }

                try {
                    let url;
                    if (selectedProduct.type === 'points') {
                        // 充值积分
                        url = `https://cloudshop.qnm6.top/mika/points.php?username=${username}&points=${selectedProduct.points}&link=${link}`;
                    } else {
                        // 充值会员
                        url = `https://cloudshop.qnm6.top/mika/vip.php?username=${username}&days=${selectedProduct.days}&link=${link}`;
                    }

                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

                    const response = await fetch(url, {
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`服务激活失败: ${response.status}`);
                    }

                    console.log('服务激活成功');
                } catch (error) {
                    console.error('服务激活失败:', error);
                    // 不显示错误给用户，因为支付已经成功了
                }
            }

            // 启动倒计时
            function startTimer() {
                timeLeft = 600; // 10分钟
                updateTimerDisplay();
                
                timerInterval = setInterval(() => {
                    timeLeft--;
                    updateTimerDisplay();
                    
                    // 更新进度条
                    const progress = ((600 - timeLeft) / 600) * 100;
                    progressBar.style.width = `${progress}%`;
                    
                    if (timeLeft <= 0) {
                        clearInterval(timerInterval);
                    }
                }, 1000);
            }

            // 更新倒计时显示
            function updateTimerDisplay() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${padZero(minutes)}:${padZero(seconds)}`;
            }

            // 复制链接
            function copyLink() {
                paymentLink.select();
                document.execCommand('copy');
                
                // 显示复制成功反馈
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                }, 2000);
            }

            // 初始化
            function init() {
                renderProducts();
                setupPaymentMethods();

                // 绑定购买按钮事件
                purchaseBtn.addEventListener('click', () => createOrder(false));

                // 绑定复制按钮事件
                copyBtn.addEventListener('click', copyLink);

                // 绑定重试按钮事件
                retryBtn.addEventListener('click', () => {
                    hideError();
                    createOrder(true);
                });

                // 检查用户名是否存在
                if (!localStorage.getItem('miru_username')) {
                    showError('请先登录后再进行购买', false);
                    purchaseBtn.disabled = true;
                }
            }

            init();
        });
    </script>
</body>
</html>